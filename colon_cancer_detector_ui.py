import tkinter as tk
from tkinter import filedialog, messagebox, ttk, scrolledtext
from PIL import Image, ImageTk
import cv2
import numpy as np
import tensorflow as tf
from tensorflow import keras
import os
import threading
from datetime import datetime
import json

# 导入LLM模块
from typing import Optional
import requests

class QianwenChat:
    """千问AI对话客户端"""

    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }

    def generate_medical_report(self, prediction_result: str, confidence: float,
                              image_features: str = "") -> Optional[str]:
        """生成医学诊断报告"""

        prompt = f"""
作为一名专业的病理学医生，请基于以下结肠细胞检测结果生成详细的医学诊断报告：

检测结果：{prediction_result}
置信度：{confidence:.2%}
图像特征：{image_features}

请生成一份专业的医学报告，包含以下内容：
1. 检测结果摘要
2. 病理学分析
3. 风险评估
4. 建议的后续检查或治疗方案
5. 注意事项

报告应该专业、准确、易于理解，适合临床医生参考。
"""

        data = {
            "model": "qwen-turbo",
            "input": {
                "messages": [{"role": "user", "content": prompt}]
            },
            "parameters": {
                "max_tokens": 2000,
                "temperature": 0.3,
                "top_p": 0.8
            }
        }

        try:
            response = requests.post(
                self.base_url,
                headers=self.headers,
                json=data,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                if result.get("output") and result["output"].get("text"):
                    return result["output"]["text"]
            return None
        except Exception as e:
            print(f"LLM报告生成错误: {e}")
            return None

class ColonCancerDetectorUI:
    def __init__(self, root):
        self.root = root
        self.root.title("智能结肠癌细胞检测与诊断系统")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f8f9fa')
        # 居中显示窗口
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')

        # 模型相关
        self.model = None
        self.img_size = (224, 224)
        self.class_names = ['癌细胞', '良性细胞']

        # 当前图像
        self.current_image = None
        self.current_image_path = None
        self.current_prediction = None
        self.current_confidence = None

        # LLM客户端
        self.llm_client = QianwenChat("sk-59b3f012029d4fa9965b999d7fb00b4f")

        # 创建样式
        self.setup_styles()
        self.setup_ui()
        self.load_model()

    def setup_styles(self):
        """设置现代化样式"""
        self.style = ttk.Style()
        self.style.theme_use('clam')

        # 配置样式
        self.style.configure('Title.TLabel',
                           font=('Microsoft YaHei', 24, 'bold'),
                           foreground='#2c3e50',
                           background='#f8f9fa')

        self.style.configure('Heading.TLabel',
                           font=('Microsoft YaHei', 14, 'bold'),
                           foreground='#34495e',
                           background='#f8f9fa')

        self.style.configure('Info.TLabel',
                           font=('Microsoft YaHei', 11),
                           foreground='#7f8c8d',
                           background='#f8f9fa')

        # 按钮样式
        self.style.configure('Primary.TButton',
                           font=('Microsoft YaHei', 12, 'bold'),
                           padding=(20, 10))

        self.style.configure('Success.TButton',
                           font=('Microsoft YaHei', 12, 'bold'),
                           padding=(20, 10))

        self.style.configure('Danger.TButton',
                           font=('Microsoft YaHei', 12, 'bold'),
                           padding=(20, 10))

    def setup_ui(self):
        """设置现代化用户界面"""
        # 主容器
        main_container = tk.Frame(self.root, bg='#f8f9fa')
        main_container.pack(fill='both', expand=True, padx=20, pady=20)

        # 标题区域
        title_frame = tk.Frame(main_container, bg='#f8f9fa')
        title_frame.pack(fill='x', pady=(0, 30))

        title_label = ttk.Label(
            title_frame,
            text="🔬 智能结肠癌细胞检测与诊断系统",
            style='Title.TLabel'
        )
        title_label.pack()

        subtitle_label = ttk.Label(
            title_frame,
            text="基于深度学习的医学影像分析与AI辅助诊断",
            style='Info.TLabel'
        )
        subtitle_label.pack(pady=(5, 0))

        # 主要内容区域 - 使用PanedWindow分割
        main_paned = ttk.PanedWindow(main_container, orient='horizontal')
        main_paned.pack(fill='both', expand=True)

        # 左侧面板 - 图像和控制
        left_frame = ttk.Frame(main_paned)
        main_paned.add(left_frame, weight=3)  # 增加左侧权重，给图像更多空间

        # 控制按钮区域 - 现代化设计
        control_frame = ttk.LabelFrame(left_frame, text="🎛️ 操作控制面板", padding=20)
        control_frame.pack(fill='x', pady=(0, 15))

        # 创建按钮容器，使用网格布局
        button_container = tk.Frame(control_frame, bg='#f8f9fa')
        button_container.pack(fill='x')

        # 按钮样式配置
        button_style = {
            'font': ("Microsoft YaHei", 11, "bold"),
            'cursor': 'hand2',
            'relief': 'flat',
            'borderwidth': 0,
            'padx': 20,
            'pady': 15,
            'width': 12,
            'activebackground': '#34495e',
            'activeforeground': 'white'
        }

        # 第一行按钮
        top_row = tk.Frame(button_container, bg='#f8f9fa')
        top_row.pack(fill='x', pady=(0, 10))

        # 上传按钮 - 蓝色主题
        self.upload_btn = tk.Button(
            top_row,
            text="📁 上传图片",
            command=self.upload_image,
            bg='#3498db',
            fg='white',
            **button_style
        )
        self.upload_btn.pack(side=tk.LEFT, padx=(0, 10), expand=True, fill='x')

        # 检测按钮 - 红色主题
        self.detect_btn = tk.Button(
            top_row,
            text="🔍 开始检测",
            command=self.detect_cancer,
            bg='#e74c3c',
            fg='white',
            state='disabled',
            **button_style
        )
        self.detect_btn.pack(side=tk.LEFT, padx=(5, 0), expand=True, fill='x')

        # 第二行按钮
        bottom_row = tk.Frame(button_container, bg='#f8f9fa')
        bottom_row.pack(fill='x')

        # 生成报告按钮 - 绿色主题
        self.report_btn = tk.Button(
            bottom_row,
            text="📋 生成报告",
            command=self.generate_report,
            bg='#27ae60',
            fg='white',
            state='disabled',
            **button_style
        )
        self.report_btn.pack(side=tk.LEFT, padx=(0, 10), expand=True, fill='x')

        # 清除按钮 - 灰色主题
        self.clear_btn = tk.Button(
            bottom_row,
            text="🗑️ 清除重置",
            command=self.clear_results,
            bg='#95a5a6',
            fg='white',
            **button_style
        )
        self.clear_btn.pack(side=tk.LEFT, padx=(5, 0), expand=True, fill='x')

        # 添加按钮悬停效果
        self._setup_button_hover_effects()

        # 图像显示区域
        image_frame = ttk.LabelFrame(left_frame, text="医学影像", padding=15)
        image_frame.pack(fill='both', expand=True)

        # 图像容器 - 设置固定最小尺寸
        image_container = tk.Frame(image_frame, bg='white', relief='sunken', bd=2)
        image_container.pack(fill='both', expand=True, padx=5, pady=5)
        image_container.configure(width=550, height=450)  # 设置最小尺寸
        image_container.pack_propagate(False)  # 防止容器收缩

        self.image_label = tk.Label(
            image_container,
            text="请上传结肠细胞显微镜图像\n\n支持格式：JPG, PNG, BMP, TIFF\n建议分辨率：224x224像素或更高",
            font=("Microsoft YaHei", 12),
            bg='white',
            fg='#7f8c8d',
            justify='center'
        )
        self.image_label.pack(expand=True, fill='both')

        # 右侧面板 - 结果和报告
        right_frame = ttk.Frame(main_paned)
        main_paned.add(right_frame, weight=1)

        # 检测结果区域
        result_frame = ttk.LabelFrame(right_frame, text="检测结果", padding=20)
        result_frame.pack(fill='x', pady=(0, 20))

        self.result_label = tk.Label(
            result_frame,
            text="等待检测...",
            font=("Microsoft YaHei", 16, "bold"),
            bg='#f8f9fa',
            fg='#7f8c8d'
        )
        self.result_label.pack(pady=(0, 10))

        self.confidence_label = tk.Label(
            result_frame,
            text="",
            font=("Microsoft YaHei", 12),
            bg='#f8f9fa',
            fg='#7f8c8d'
        )
        self.confidence_label.pack()

        # AI诊断报告区域
        report_frame = ttk.LabelFrame(right_frame, text="AI智能诊断报告", padding=20)
        report_frame.pack(fill='both', expand=True)

        # 报告文本区域
        self.report_text = scrolledtext.ScrolledText(
            report_frame,
            wrap=tk.WORD,
            font=("Microsoft YaHei", 11),
            bg='white',
            fg='#2c3e50',
            relief='flat',
            borderwidth=1,
            state='disabled'
        )
        self.report_text.pack(fill='both', expand=True)

        # 底部状态栏
        status_frame = tk.Frame(main_container, bg='#f8f9fa')
        status_frame.pack(fill='x', pady=(20, 0))

        # 进度条
        self.progress = ttk.Progressbar(
            status_frame,
            mode='indeterminate',
            length=400
        )

        # 状态标签
        self.status_label = tk.Label(
            status_frame,
            text="系统就绪 - 请上传医学影像开始检测",
            font=("Microsoft YaHei", 10),
            bg='#f8f9fa',
            fg='#27ae60'
        )
        self.status_label.pack(side=tk.LEFT)

    def _setup_button_hover_effects(self):
        """设置按钮悬停效果"""
        # 定义悬停颜色
        hover_colors = {
            self.upload_btn: ('#2980b9', '#3498db'),      # 深蓝 -> 浅蓝
            self.detect_btn: ('#c0392b', '#e74c3c'),      # 深红 -> 浅红
            self.report_btn: ('#229954', '#27ae60'),      # 深绿 -> 浅绿
            self.clear_btn: ('#7f8c8d', '#95a5a6')        # 深灰 -> 浅灰
        }

        for button, (hover_color, normal_color) in hover_colors.items():
            # 绑定鼠标进入事件
            button.bind("<Enter>", lambda _, btn=button, color=hover_color:
                       btn.config(bg=color) if btn['state'] != 'disabled' else None)

            # 绑定鼠标离开事件
            button.bind("<Leave>", lambda _, btn=button, color=normal_color:
                       btn.config(bg=color) if btn['state'] != 'disabled' else None)
    
    def load_model(self):
        """加载训练好的模型"""
        try:
            model_path = 'colon_cancer_classifier.h5'
            if os.path.exists(model_path):
                self.model = keras.models.load_model(model_path)
                self.status_label.config(text="模型加载成功", fg='#27ae60')
            else:
                # 尝试加载备用模型
                backup_path = 'best_colon_cancer_model.h5'
                if os.path.exists(backup_path):
                    self.model = keras.models.load_model(backup_path)
                    self.status_label.config(text="备用模型加载成功", fg='#27ae60')
                else:
                    self.status_label.config(text="未找到模型文件，请先训练模型", fg='#e74c3c')
                    messagebox.showwarning(
                        "警告", 
                        "未找到训练好的模型文件！\n请先运行 colon_cancer_classifier.py 训练模型。"
                    )
        except Exception as e:
            self.status_label.config(text=f"模型加载失败: {str(e)}", fg='#e74c3c')
            messagebox.showerror("错误", f"模型加载失败: {str(e)}")
    
    def upload_image(self):
        """上传图片"""
        file_types = [
            ("图片文件", "*.jpg *.jpeg *.png *.bmp *.tiff"),
            ("JPEG文件", "*.jpg *.jpeg"),
            ("PNG文件", "*.png"),
            ("所有文件", "*.*")
        ]
        
        file_path = filedialog.askopenfilename(
            title="选择图片文件",
            filetypes=file_types
        )
        
        if file_path:
            try:
                # 加载并显示图片
                self.current_image_path = file_path
                self.display_image(file_path)
                self.detect_btn.config(state='normal')
                self.status_label.config(text="图片上传成功", fg='#27ae60')
                
                # 清除之前的结果
                self.result_label.config(text="")
                self.confidence_label.config(text="")
                
            except Exception as e:
                messagebox.showerror("错误", f"无法加载图片: {str(e)}")
                self.status_label.config(text="图片加载失败", fg='#e74c3c')
    
    def display_image(self, image_path):
        """显示图片 - 支持更大尺寸"""
        try:
            # 加载图片
            image = Image.open(image_path)

            # 设置更大的显示尺寸
            max_width = 500
            max_height = 400

            # 获取原始图片尺寸
            orig_width, orig_height = image.size

            # 计算缩放比例，保持宽高比
            width_ratio = max_width / orig_width
            height_ratio = max_height / orig_height
            scale_ratio = min(width_ratio, height_ratio)

            # 如果图片比目标尺寸小，则放大到合适大小
            if scale_ratio < 1:
                new_width = int(orig_width * scale_ratio)
                new_height = int(orig_height * scale_ratio)
            else:
                # 如果图片很小，适当放大
                scale_ratio = min(2.0, min(max_width / orig_width, max_height / orig_height))
                new_width = int(orig_width * scale_ratio)
                new_height = int(orig_height * scale_ratio)

            # 调整图片大小
            image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)

            # 转换为PhotoImage
            photo = ImageTk.PhotoImage(image)

            # 更新标签
            self.image_label.config(image=photo, text="", compound='center')
            self.image_label.image = photo  # 保持引用

        except Exception as e:
            messagebox.showerror("错误", f"无法显示图片: {str(e)}")
    
    def detect_cancer(self):
        """检测癌细胞 - 增强版"""
        if self.model is None:
            messagebox.showerror("错误", "模型未加载，无法进行检测")
            return

        if self.current_image_path is None:
            messagebox.showerror("错误", "请先上传图片")
            return

        try:
            # 显示进度条
            self.progress.pack(pady=10)
            self.progress.start()
            self.status_label.config(text="🔍 正在进行AI智能检测分析...", fg='#f39c12')
            self.root.update()

            # 预处理图像
            img = cv2.imread(self.current_image_path)
            img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            img = cv2.resize(img, self.img_size)
            img = img.astype('float32') / 255.0
            img = np.expand_dims(img, axis=0)

            # 进行预测
            prediction = self.model.predict(img, verbose=0)
            predicted_class = np.argmax(prediction, axis=1)[0]
            confidence = np.max(prediction)

            # 保存预测结果
            self.current_prediction = self.class_names[predicted_class]
            self.current_confidence = confidence

            # 停止进度条
            self.progress.stop()
            self.progress.pack_forget()

            # 显示结果
            class_name = self.class_names[predicted_class]

            # 设置结果颜色和图标
            if predicted_class == 0:  # 癌细胞
                result_color = '#e74c3c'
                status_text = "⚠️ 检测到癌细胞特征"
                result_icon = "🔴"
            else:  # 良性
                result_color = '#27ae60'
                status_text = "✅ 检测到良性细胞特征"
                result_icon = "🟢"

            self.result_label.config(
                text=f"{result_icon} 检测结果: {class_name}",
                fg=result_color
            )

            self.confidence_label.config(
                text=f"🎯 置信度: {confidence:.2%} | 📊 准确性: {'高' if confidence > 0.8 else '中等' if confidence > 0.6 else '低'}"
            )

            self.status_label.config(text=status_text, fg=result_color)

            # 启用报告生成按钮
            self.report_btn.config(state='normal')

            # 在报告区域显示初步结果
            self.update_report_preview(class_name, confidence, predicted_class)

        except Exception as e:
            # 停止进度条
            self.progress.stop()
            self.progress.pack_forget()

            messagebox.showerror("错误", f"检测失败: {str(e)}")
            self.status_label.config(text="❌ 检测失败", fg='#e74c3c')

    def update_report_preview(self, class_name, confidence, predicted_class):
        """更新报告预览"""
        self.report_text.config(state='normal')
        self.report_text.delete(1.0, tk.END)

        preview_text = f"""📋 检测结果预览
{'='*50}

🔬 检测结果: {class_name}
🎯 置信度: {confidence:.2%}
📅 检测时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

{'⚠️ 风险提示:' if predicted_class == 0 else '✅ 初步评估:'}
{('检测到疑似癌细胞特征，建议立即进行进一步医学检查和病理确认。' if predicted_class == 0 else '检测结果显示为良性细胞特征，但仍建议定期复查。')}

💡 提示: 点击"生成报告"按钮获取AI智能生成的详细医学诊断报告
"""

        self.report_text.insert(tk.END, preview_text)
        self.report_text.config(state='disabled')

    def generate_report(self):
        """生成详细的AI诊断报告"""
        if self.current_prediction is None or self.current_confidence is None:
            messagebox.showwarning("警告", "请先进行检测")
            return

        # 在新线程中生成报告，避免界面卡顿
        threading.Thread(target=self._generate_report_async, daemon=True).start()

    def _generate_report_async(self):
        """异步生成报告"""
        try:
            # 更新状态
            self.root.after(0, lambda: self.status_label.config(
                text="🤖 AI正在生成详细诊断报告...", fg='#f39c12'))
            self.root.after(0, lambda: self.progress.pack(pady=10))
            self.root.after(0, lambda: self.progress.start())

            # 生成图像特征描述
            image_features = self._analyze_image_features()

            # 调用LLM生成报告
            report = self.llm_client.generate_medical_report(
                self.current_prediction,
                self.current_confidence,
                image_features
            )

            # 更新UI
            self.root.after(0, lambda: self._update_report_ui(report))

        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("错误", f"报告生成失败: {str(e)}"))
            self.root.after(0, lambda: self.status_label.config(text="❌ 报告生成失败", fg='#e74c3c'))
        finally:
            self.root.after(0, lambda: self.progress.stop())
            self.root.after(0, lambda: self.progress.pack_forget())

    def _analyze_image_features(self):
        """分析图像特征"""
        try:
            img = cv2.imread(self.current_image_path)
            img_gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

            # 基本图像统计
            mean_intensity = np.mean(img_gray)
            std_intensity = np.std(img_gray)

            # 纹理分析
            contrast = np.std(img_gray)

            features = f"图像亮度均值: {mean_intensity:.2f}, 标准差: {std_intensity:.2f}, 对比度: {contrast:.2f}"
            return features
        except:
            return "图像特征分析暂不可用"

    def _update_report_ui(self, report):
        """更新报告UI"""
        self.report_text.config(state='normal')
        self.report_text.delete(1.0, tk.END)

        if report:
            # 添加报告头部
            header = f"""🏥 AI智能诊断报告
{'='*60}
📅 生成时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}
🔬 检测结果: {self.current_prediction}
🎯 置信度: {self.current_confidence:.2%}
{'='*60}

"""
            self.report_text.insert(tk.END, header)
            self.report_text.insert(tk.END, report)

            # 添加免责声明
            disclaimer = f"""

{'='*60}
⚠️ 重要声明:
本报告由AI系统生成，仅供医学参考，不能替代专业医生的诊断。
最终诊断结果请以专业医疗机构的病理检查为准。
建议在专业医生指导下进行进一步检查和治疗。
{'='*60}
"""
            self.report_text.insert(tk.END, disclaimer)

            self.status_label.config(text="✅ AI诊断报告生成完成", fg='#27ae60')
        else:
            self.report_text.insert(tk.END, "❌ 报告生成失败，请检查网络连接或稍后重试")
            self.status_label.config(text="❌ 报告生成失败", fg='#e74c3c')

        self.report_text.config(state='disabled')
    
    def clear_results(self):
        """清除所有结果"""
        self.current_image_path = None
        self.current_prediction = None
        self.current_confidence = None

        # 清除图像
        self.image_label.config(
            image="",
            text="请上传结肠细胞显微镜图像\n\n支持格式：JPG, PNG, BMP, TIFF\n建议分辨率：224x224像素或更高"
        )
        self.image_label.image = None

        # 清除结果
        self.result_label.config(text="等待检测...", fg='#7f8c8d')
        self.confidence_label.config(text="")

        # 清除报告
        self.report_text.config(state='normal')
        self.report_text.delete(1.0, tk.END)
        self.report_text.config(state='disabled')

        # 重置按钮状态
        self.detect_btn.config(state='disabled')
        self.report_btn.config(state='disabled')

        # 重置状态
        self.status_label.config(text="系统就绪 - 请上传医学影像开始检测", fg='#27ae60')


def main():
    """主函数"""
    root = tk.Tk()
    ColonCancerDetectorUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
